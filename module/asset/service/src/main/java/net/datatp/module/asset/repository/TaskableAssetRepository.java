package net.datatp.module.asset.repository;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.asset.entity.TaskableAsset;

public interface TaskableAssetRepository extends JpaRepository<TaskableAsset, Serializable>{
  
  @Query( "SELECT a from TaskableAsset a WHERE id = :id" )
  TaskableAsset getById(@Param("id") Long id);
  
  @Query("SELECT a FROM TaskableAsset a WHERE a.assetId = :assetId")
  List<TaskableAsset> findByAssetId(@Param("assetId") Long assetId);
  
  //                a.from           from       a.to    to
  //    from       a.from      to                a.to
  //               a.from      from       to      a.to
  
  @Query("SELECT a FROM TaskableAsset a WHERE a.assetId = :assetId "
      + "AND ("
      + "(a.fromTime <= :fromTime AND a.toTime >= :fromTime) OR "
      + "(a.fromTime <= :toTime   AND a.toTime >= :toTime)"
      + ")")
  List<TaskableAsset> findUsedAssets(@Param("assetId") Long assetId, @Param("fromTime") Date fromTime, @Param("toTime") Date toTime);
}
